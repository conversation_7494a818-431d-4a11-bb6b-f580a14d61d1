import { IMicroserviceContext } from '../../../shared/interfaces'
import { GetCatalogByIdResponse } from '../models/get-catalog-by-id'
import {
  catalog,
  catalog_genre,
  catalog_user,
  content,
  genre
} from '../database/schema'
import { and, count, eq, inArray } from 'drizzle-orm'
import { take_uniq_or_throw } from '../database/utils'
import tracedTreaty from '../../../shared/setup/traced-treaty'
import { service_resolver } from '../../../shared/utils/service-resolver'
import { Service } from '../../../shared/enums'
import { UserApp } from '@noice/user-service/app'
import { CommunityApp } from '@noice/community-service/app'

type CatalogRecord = typeof catalog.$inferSelect

export async function enrichCatalogData(
  catalog_resp: CatalogRecord,
  { traced_db, redis }: IMicroserviceContext
): Promise<GetCatalogByIdResponse> {
  const cached = await redis.get(`catalog:enrichCatalogData:${catalog_resp.id}`)
  if (cached) {
    return JSON.parse(cached)
  }

  // Run all queries in parallel using Promise.all
  const [content_count, genre_ids, creators_list] = await Promise.all([
    traced_db.trace('countContent', () =>
      traced_db.db
        .select({
          count: count(content.id)
        })
        .from(content)
        .where(
          and(
            eq(content.catalog_id, catalog_resp.id),
            eq(content.status, 'active')
          )
        )
        .execute()
        .then(take_uniq_or_throw)
    ),

    traced_db.trace('getGenreIds', () =>
      traced_db.db
        .select()
        .from(catalog_genre)
        .where(eq(catalog_genre.catalog_id, catalog_resp.id))
        .execute()
        .then((res) => res.map((r) => r.genre_id))
    ),

    traced_db.trace('getCreatorIds', () =>
      traced_db.db
        .select()
        .from(catalog_user)
        .where(eq(catalog_user.catalog_id, catalog_resp.id))
        .execute()
        .then((res) => res.map((r) => ({ user_id: r.user_id, type: r.type })))
    )
  ])

  // Now run the next set of queries that depend on the first set
  const [genres, creators, analytics] = await Promise.all([
    traced_db.trace('getGenres', () =>
      traced_db.db
        .select()
        .from(genre)
        .where(inArray(genre.id, genre_ids))
        .execute()
        .then((res) =>
          res.map((r) => ({
            slug: r.slug || '',
            name: r.name
          }))
        )
    ),

    tracedTreaty<UserApp>(service_resolver.getServiceUrl(Service.USER))
      .users.get({
        query: {
          ids: creators_list.map((c) => c.user_id).join(',')
        }
      })
      .then((res) => res.data),

    tracedTreaty<CommunityApp>(
      service_resolver.getServiceUrl(Service.COMMUNITY)
    )
      .community.catalog({
        id: catalog_resp.id
      })
      .analytics.get()
      .then((res) => res.data)
  ])

  const content_creators: GetCatalogByIdResponse['creators'] =
    creators_list.reduce(
      (accu: GetCatalogByIdResponse['creators'], creator) => {
        const user = (creators || []).find((c) => c.id === creator.user_id)
        if (user) {
          accu.push({
            display_name: user.profile.display_name,
            user_name: user.user_name,
            photo: user.profile.photo,
            type: creator.type || 'host'
          })
        }
        return accu
      },
      []
    )

  const resp: GetCatalogByIdResponse = {
    id: catalog_resp.id,
    title: catalog_resp.title,
    description:
      catalog_resp.html_description || catalog_resp.description || '',
    type:
      catalog_resp.type === 'livestream' ||
      catalog_resp.type === 'radio' ||
      !catalog_resp.type
        ? 'podcast'
        : catalog_resp.type,
    schedule: (catalog_resp.data as { schedule?: string })?.schedule || '',
    source: catalog_resp.source || 'hosting',
    image:
      (catalog_resp.image_meta as { [key: string]: string })?.['300'] ||
      catalog_resp.image ||
      '',
    genres,
    creators: content_creators,
    published_at: catalog_resp.published_at,
    seo: {
      og_title: catalog_resp.title,
      og_description:
        catalog_resp.description || catalog_resp.html_description || '',
      og_image:
        (catalog_resp.image_meta as { [key: string]: string })?.['300'] ||
        catalog_resp.image ||
        ''
    },
    stats: {
      followers: analytics?.total_followers || 0,
      episodes: content_count.count
    }
  }

  await redis.set(
    `catalog:enrichCatalogData:${catalog_resp.id}`,
    JSON.stringify(resp),
    'EX',
    60
  )

  return resp
}
